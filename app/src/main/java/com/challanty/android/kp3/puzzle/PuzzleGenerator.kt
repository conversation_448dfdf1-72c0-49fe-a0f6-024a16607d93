package com.challanty.android.kp3.puzzle

interface PuzzleGenerator {
    fun generatePuzzle(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
        seed: Long? = null,
        isFlipOU: Boolean = false
    ): Puzzle

    fun scramblePuzzle(
        puzzle: Puzzle,
        doRotations: <PERSON><PERSON><PERSON>,
    ): Puzzle

    fun swapTiles(
        puzzle: Puzzle,
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    )

    fun rotateTile(
        puzzle: Puzzle,
        boardRow: Int,
        boardCol: Int,
    )
}