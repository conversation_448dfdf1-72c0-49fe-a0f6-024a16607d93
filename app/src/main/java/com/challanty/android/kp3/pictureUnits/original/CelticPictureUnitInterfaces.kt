package com.challanty.android.kp3.pictureUnits.original

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Path

/**
 * Interface for Celtic picture unit paths.
 * Provides access to the different paths that make up a Celtic picture unit.
 */
interface CelticPictureUnitPaths {
    fun getRibbonPath(): Path
    fun getRibbonBGPath(): Path
    fun getOutlinePath(): Path
}

/**
 * Interface for Celtic picture unit strategy.
 * Defines how to create paths for a specific Celtic picture unit.
 */
interface CelticPicUnitStrategy {
    /**
     * Creates the paths for this Celtic picture unit.
     * @param center The center point of the picture unit
     * @param radius The radius of the picture unit
     * @return The created Celtic picture unit paths
     */
    fun createPaths(center: Offset, radius: Float): CelticPaths
}
