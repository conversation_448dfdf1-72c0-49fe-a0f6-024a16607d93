package com.challanty.android.kp3.puzzle.original

import com.challanty.android.kp3.puzzle.Puzzle
import com.challanty.android.kp3.puzzle.PuzzleService
import com.challanty.android.kp3.viewModel.TileModel

/**
 * Service for managing puzzle generation and operations.
 * Follows the service pattern to encapsulate puzzle-related functionality.
 */
class OriginalPuzzleService(): PuzzleService {
    private val generator = OriginalPuzzleGenerator()
    private val solver = OriginalPuzzleSolver()

    override fun generatePuzzle(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
        seed: Long?,
        isFlipOU: Boolean
    ): Puzzle {

        // Generate the pattern matrix
        return generator.generatePuzzle(
            rows,
            cols,
            tileRows,
            tileCols,
            seed,
            isFlipOU
        )
    }

    override fun scramblePuzzle(
        puzzle: Puzzle,
        doRotations: Boolean,
    ): Puzzle {
        return generator.scramblePuzzle(
            puzzle,
            doRotations,
        )
    }

    override fun swapTiles(
        puzzle: Puzzle,
        tile1: TileModel,
        tile2: TileModel,
    ) {
        generator.swapTiles(
            puzzle = puzzle,
            boardRow1 = tile1.boardPosition.x,
            boardCol1 = tile1.boardPosition.y,
            boardRow2 = tile2.boardPosition.x,
            boardCol2 = tile2.boardPosition.y,
        )
    }

    override fun rotateTile(
        puzzle: Puzzle,
        tile: TileModel,
    ) {
        generator.rotateTile(
            puzzle = puzzle,
            boardRow = tile.boardPosition.x,
            boardCol = tile.boardPosition.y,
        )
    }

    override fun isPuzzleSolved(
        puzzle: Puzzle,
    ): Boolean {
        return solver.isPuzzleSolved(puzzle)
    }
}