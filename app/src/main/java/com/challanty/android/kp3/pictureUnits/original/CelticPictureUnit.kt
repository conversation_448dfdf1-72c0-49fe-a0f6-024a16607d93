package com.challanty.android.kp3.pictureUnits.original

import android.graphics.Matrix
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathOperation
import androidx.compose.ui.graphics.asAndroidPath
import kotlin.math.sqrt

/**
 * Default implementation of CelticPaths.
 * Stores the ribbon, ribbon background, and outline paths.
 */
class CelticPaths(
    private val ribbon: Path,
    private val ribbonBG: Path,
    private val outline: Path
) : CelticPictureUnitPaths {
    override fun getRibbonPath(): Path = ribbon
    override fun getRibbonBGPath(): Path = ribbonBG
    override fun getOutlinePath(): Path = outline
}

/**
 * Constants for standardized Celtic picture unit drawing.
 */
object CelticPicUnitConstants {
    // Rotation angles in degrees
    const val ROTATION_90 = 90f
    const val ROTATION_180 = 180f
    const val ROTATION_270 = 270f

    /**
     * Creates a rotated version of a picture unit by applying a rotation transformation.
     *
     * @param originalPictureUnit The original picture unit strategy to rotate
     * @param rotationDegrees The rotation angle in degrees (clockwise)
     * @return A new picture unit strategy that creates rotated paths
     */
    fun createRotatedPictureUnit(originalPictureUnit: CelticPicUnitStrategy, rotationDegrees: Float): CelticPicUnitStrategy {
        return object : CelticPicUnitStrategy {
            override fun createPaths(center: Offset, radius: Float): CelticPaths {
                // Get the original paths
                val originalPaths = originalPictureUnit.createPaths(center, radius)

                // Create rotated versions of each path
                val rotatedRibbon =
                    rotatePath(originalPaths.getRibbonPath(), center, rotationDegrees)
                val rotatedRibbonBG =
                    rotatePath(originalPaths.getRibbonBGPath(), center, rotationDegrees)
                val rotatedOutline =
                    rotatePath(originalPaths.getOutlinePath(), center, rotationDegrees)

                // Return the rotated paths
                return CelticPaths(
                    ribbon = rotatedRibbon,
                    ribbonBG = rotatedRibbonBG,
                    outline = rotatedOutline
                )
            }

            /**
             * Rotates a path around a center point by the specified angle.
             *
             * @param path The path to rotate
             * @param center The center point to rotate around
             * @param degrees The rotation angle in degrees (clockwise)
             * @return A new rotated path
             */
            private fun rotatePath(path: Path, center: Offset, degrees: Float): Path {
                // Create a new path for the rotated result
                val rotatedPath = Path()

                // Create a matrix for the rotation transformation
                val matrix = Matrix()

                // Apply the rotation around the center point
                matrix.setRotate(degrees, center.x, center.y)

                // Apply the transformation to the path
                path.asAndroidPath().transform(matrix, rotatedPath.asAndroidPath())

                return rotatedPath
            }
        }
    }

    /**
     * Creates an inverse path by subtracting the given path from a rectangle.
     *
     * @param path The path to invert
     * @param boxW The width of the bounding box
     * @param boxH The height of the bounding box
     * @return A new path that represents the inverse of the input path
     */
    fun getInversePath(path: Path, boxW: Float, boxH: Float): Path {
        // Create a rectangle that covers the same area as the path
        val rect = Rect(0f, 0f, boxW, boxH)

        // Create a new path and add the rectangle to it
        val inversePath = Path()
        inversePath.addRect(rect)

        // Subtract the original path from the rectangle using the Op.DIFFERENCE operator
        inversePath.op(inversePath, path, PathOperation.Difference)

        return inversePath
    }

    // Size of a picture unit before scaling to size of tile cell.
    // PU width and height are always the same.
    const val PU_BOX_SIZE = 100f  // Size of "box" used for defining picUnit drawing paths
    const val RIBBON_W = PU_BOX_SIZE / 2f
    // cornerOffset and sideOffset are the distances from a pic corner to a
    // ribbon side when the ribbon exits or enters a pic at a corner or side,
    // respectively.
    val CORNER_OFFSET = sqrt((RIBBON_W * RIBBON_W) / 2f)
    const val SIDE_OFFSET = (PU_BOX_SIZE - RIBBON_W) / 2f
}

/**
 * Abstract base class for Celtic patterns that handles common initialization code.
 * This class standardizes the pattern parameter calculations and provides a template
 * method for creating pattern paths.
 */
abstract class AbstractCelticPicUnit : CelticPicUnitStrategy {

    companion object {
        fun rotatePicUnit(
            picUnit: Int,
            quarterTurns: Int
        ): Int {
            // Return the picture unit ID for pictureUnit rotated clockwise by number of
            // 90 degree quarter turns (0, 1, 2, 3)
            // Note: All base picture units have 4 rotations except y which has 2
            val normalizedPicID = picUnit % 4

            val rotatedPicID: Int = if (normalizedPicID + quarterTurns < 4) {
                picUnit + quarterTurns
            } else {
                picUnit + quarterTurns - 4
            }

            // Correct for picture units that have only 2 rotations (y and z). z has ID 25
            return if (rotatedPicID < 26) {
                rotatedPicID
            } else {
                rotatedPicID - 2
            }
        }
    }
    override fun createPaths(center: Offset, radius: Float): CelticPaths {
        val scale = (radius * 2) / CelticPicUnitConstants.PU_BOX_SIZE

        val x0 = 0.0f
        val y0 = 0.0f

        val boxW = CelticPicUnitConstants.PU_BOX_SIZE * scale
        val boxH = CelticPicUnitConstants.PU_BOX_SIZE * scale
        val ribbonW = CelticPicUnitConstants.RIBBON_W * scale
        val cornerOffset = CelticPicUnitConstants.CORNER_OFFSET * scale
        val sideOffset = CelticPicUnitConstants.SIDE_OFFSET * scale

        // Call the abstract method that subclasses will implement
        return createPicUnitPaths(x0, y0, boxW, boxH, ribbonW, cornerOffset, sideOffset)
    }

    /**
     * Abstract method that subclasses must implement to create their specific pattern paths.
     *
     * @param x0 The x-coordinate of the origin point
     * @param y0 The y-coordinate of the origin point
     * @param boxW The width of the bounding box
     * @param boxH The height of the bounding box
     * @param ribbonW The width of the ribbon
     * @param cornerOffset The offset from a corner
     * @param sideOffset The offset from a side
     * @return The created Celtic pattern paths
     */
    protected abstract fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths
}

/**
 * Abstract base class for rotated Celtic patterns.
 * This class handles the rotation of a base pattern.
 */
abstract class AbstractRotatedCelticPicUnit : CelticPicUnitStrategy {
    /**
     * Get the base pattern to rotate.
     * @return The base pattern to rotate
     */
    protected abstract fun getBasePictureUnit(): CelticPicUnitStrategy

    /**
     * Get the rotation angle in degrees.
     * @return The rotation angle in degrees
     */
    protected abstract fun getRotationAngle(): Float

    override fun createPaths(center: Offset, radius: Float): CelticPaths {
        return CelticPicUnitConstants.createRotatedPictureUnit(
            getBasePictureUnit(),
            getRotationAngle()
        ).createPaths(center, radius)
    }
}

class CelticPicUnitA : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(cornerOffset, y0)
            cubicTo(
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxH
            )
            lineTo(sideOffset, boxH)
            cubicTo(
                sideOffset, sideOffset + cornerOffset,
                sideOffset, sideOffset + cornerOffset,
                x0, cornerOffset
            )
            lineTo(x0, y0)
            close()
        }

        val outlinePath = Path().apply {
            moveTo(cornerOffset, y0)
            cubicTo(
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxH
            )
            moveTo(sideOffset, boxH)
            cubicTo(
                sideOffset, sideOffset + cornerOffset,
                sideOffset, sideOffset + cornerOffset,
                x0, cornerOffset
            )
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitE : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(cornerOffset, y0)
            cubicTo(
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxH
            )
            lineTo(sideOffset, boxH)
            cubicTo(
                sideOffset, sideOffset + cornerOffset,
                sideOffset, sideOffset + cornerOffset,
                x0, cornerOffset
            )
            lineTo(x0, y0)
            close()
        }

        val outlinePath = Path().apply {
            moveTo(cornerOffset, y0)
            cubicTo(
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxW - sideOffset - cornerOffset,
                boxW - sideOffset,
                boxH
            )
            moveTo(sideOffset, boxH)
            cubicTo(
                sideOffset, sideOffset + cornerOffset,
                sideOffset, sideOffset + cornerOffset,
                x0, cornerOffset
            )
            lineTo(cornerOffset, y0)
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitI : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(boxW - cornerOffset, y0)
            cubicTo(
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH
            )
            lineTo(boxW - sideOffset, boxH)
            cubicTo(
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW,
                cornerOffset
            )
            lineTo(boxW, y0)
            close()
        }

        val outlinePath = Path().apply {
            moveTo(boxW - cornerOffset, y0)
            cubicTo(
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH
            )
            moveTo(boxW - sideOffset, boxH)
            cubicTo(
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW,
                cornerOffset
            )
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitM : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(boxW - cornerOffset, y0)
            cubicTo(
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH
            )
            lineTo(boxW - sideOffset, boxH)
            cubicTo(
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW,
                cornerOffset
            )
            lineTo(boxW, y0)
            close()
        }

        val outlinePath = Path().apply {
            moveTo(boxW - cornerOffset, y0)
            cubicTo(
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH - cornerOffset - sideOffset,
                sideOffset,
                boxH
            )
            moveTo(boxW - sideOffset, boxH)
            cubicTo(
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW - sideOffset,
                boxH + sideOffset + cornerOffset - boxW,
                boxW,
                cornerOffset
            )
            lineTo(boxW - cornerOffset, y0)
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitU : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(sideOffset, boxH)
            cubicTo(
                sideOffset, sideOffset,
                sideOffset, sideOffset,
                boxW, sideOffset
            )
            lineTo(boxW, sideOffset + ribbonW)
            cubicTo(
                boxW - sideOffset,
                boxH - sideOffset,
                boxW - sideOffset,
                boxH - sideOffset,
                boxW - sideOffset,
                boxH
            )
            close()
        }

        val outlinePath = Path().apply {
            moveTo(sideOffset, boxH)
            cubicTo(
                sideOffset, sideOffset,
                sideOffset, sideOffset,
                boxW, sideOffset
            )
            moveTo(boxW, sideOffset + ribbonW)
            cubicTo(
                boxW - sideOffset,
                boxH - sideOffset,
                boxW - sideOffset,
                boxH - sideOffset,
                boxW - sideOffset,
                boxH
            )
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitQ : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(x0, y0)
            lineTo(x0, cornerOffset)
            lineTo(boxW - cornerOffset, boxH)
            lineTo(boxW, boxH)
            lineTo(boxW, boxH - cornerOffset)
            lineTo(cornerOffset, y0)
            close()
        }

        val outlinePath = Path().apply {
            moveTo(boxW, boxH - cornerOffset)
            lineTo(cornerOffset, y0)
            lineTo(x0, cornerOffset)
            lineTo(boxW - cornerOffset, boxH)
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitY : AbstractCelticPicUnit() {
    override fun createPicUnitPaths(
        x0: Float,
        y0: Float,
        boxW: Float,
        boxH: Float,
        ribbonW: Float,
        cornerOffset: Float,
        sideOffset: Float
    ): CelticPaths {
        val ribbonPath = Path().apply {
            moveTo(sideOffset, y0)
            lineTo(sideOffset, boxH)
            lineTo(sideOffset + ribbonW, boxH)
            lineTo(sideOffset + ribbonW, y0)
            close()
        }

        val outlinePath = Path().apply {
            moveTo(sideOffset, y0)
            lineTo(sideOffset, boxH)
            moveTo(sideOffset + ribbonW, boxH)
            lineTo(sideOffset + ribbonW, y0)
        }

        val ribbonBGPath = CelticPicUnitConstants.getInversePath(ribbonPath, boxW, boxH)

        return CelticPaths(
            ribbon = ribbonPath,
            ribbonBG = ribbonBGPath,
            outline = outlinePath
        )
    }
}

class CelticPicUnitB : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitA()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}

class CelticPicUnitC : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitA()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_180
}

class CelticPicUnitD : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitA()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_270
}

class CelticPicUnitF : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitE()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}

class CelticPicUnitG : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitE()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_180
}

class CelticPicUnitH : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitE()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_270
}

class CelticPicUnitJ : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitI()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}

class CelticPicUnitK : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitI()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_180
}

class CelticPicUnitL : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitI()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_270
}

class CelticPicUnitN : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitM()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}

class CelticPicUnitO : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitM()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_180
}

class CelticPicUnitP : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitM()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_270
}

class CelticPicUnitR : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitQ()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}

class CelticPicUnitS : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitQ()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_180
}

class CelticPicUnitT : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitQ()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_270
}

class CelticPicUnitV : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitU()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}

class CelticPicUnitW : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitU()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_180
}

class CelticPicUnitX : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitU()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_270
}

class CelticPicUnitZ : AbstractRotatedCelticPicUnit() {
    override fun getBasePictureUnit(): CelticPicUnitStrategy = CelticPicUnitY()
    override fun getRotationAngle(): Float = CelticPicUnitConstants.ROTATION_90
}