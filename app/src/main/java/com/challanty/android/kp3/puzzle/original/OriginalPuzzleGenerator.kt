package com.challanty.android.kp3.puzzle.original

import com.challanty.android.kp3.pictureUnits.original.AbstractCelticPicUnit.Companion.rotatePicUnit
import com.challanty.android.kp3.puzzle.Puzzle
import com.challanty.android.kp3.puzzle.PuzzleGenerator
import kotlin.random.Random

/**
 * A puzzle generator that creates puzzles based on boundary walls.
 *
 * This generator uses a boundary matrix to determine the pattern for each cell in the puzzle.
 * Each cell in the boundary matrix specifies the walls bounding the pattern in the corresponding
 * puzzle matrix cell.
 *
 * The generator randomly places interior walls in the boundary matrix, which determines the
 * patterns in the puzzle matrix. The pattern at each cell is determined by:
 * - The walls in the corresponding boundary cell
 * - Whether the row is even/odd
 * - Whether the column is even/odd
 *
 * The resulting puzzle forms Celtic-knot-like looping paths that may go over and under themselves
 * and each other. A puzzle is considered solved when the picture shows only looping paths with
 * no dead ends.
 */
class OriginalPuzzleGenerator : PuzzleGenerator {

    private val noWalls = 0b0000
    private val topWall = 0b0001
    private val rhtWall = 0b0010
    private val botWall = 0b0100
    private val lftWall = 0b1000

    // Convenience combinations
    private val topLftCor = topWall + lftWall
    private val topRhtCor = topWall + rhtWall
    private val botLftCor = botWall + lftWall
    private val botRhtCor = botWall + rhtWall
    private val vHallway = lftWall + rhtWall
    private val hHallway = topWall + botWall

    private val maxWallIdx = botLftCor

    // Lookup table for pattern IDs based on row and column parity (even/odd)
    // plus wall configuration.
    private val walls2ID = Array(2) { Array(2) { IntArray(maxWallIdx + 1) } }
    private val id2Links = Array(26) { IntArray(4) }

    init {
        // Initialize the walls2ID lookup table
        val er = 0 // even row
        val or = 1 // odd row
        val ec = 0 // even column
        val oc = 1 // odd column

        // Single walls
        walls2ID[er][oc][rhtWall] = 0  // These are pattern IDs
        walls2ID[or][oc][botWall] = 1
        walls2ID[or][ec][lftWall] = 2
        walls2ID[er][ec][topWall] = 3

        walls2ID[or][ec][rhtWall] = 4  // Opposite row/col parity of 0-3
        walls2ID[er][ec][botWall] = 5
        walls2ID[er][oc][lftWall] = 6
        walls2ID[or][oc][topWall] = 7

        walls2ID[or][oc][lftWall] = 8
        walls2ID[or][ec][topWall] = 9
        walls2ID[er][ec][rhtWall] = 10
        walls2ID[er][oc][botWall] = 11

        walls2ID[er][ec][lftWall] = 12  // Opposite row/col parity of 8-11
        walls2ID[er][oc][topWall] = 13
        walls2ID[or][oc][rhtWall] = 14
        walls2ID[or][ec][botWall] = 15

        // No walls
        walls2ID[or][ec][noWalls] = 16
        walls2ID[er][ec][noWalls] = 17
        walls2ID[er][oc][noWalls] = 18
        walls2ID[or][oc][noWalls] = 19

        // Corner walls
        walls2ID[er][ec][topLftCor] = 20
        walls2ID[er][oc][topLftCor] = 20
        walls2ID[or][oc][topLftCor] = 20
        walls2ID[or][ec][topLftCor] = 20

        walls2ID[er][ec][topRhtCor] = 21
        walls2ID[er][oc][topRhtCor] = 21
        walls2ID[or][oc][topRhtCor] = 21
        walls2ID[or][ec][topRhtCor] = 21

        walls2ID[er][ec][botRhtCor] = 22
        walls2ID[er][oc][botRhtCor] = 22
        walls2ID[or][oc][botRhtCor] = 22
        walls2ID[or][ec][botRhtCor] = 22

        walls2ID[er][ec][botLftCor] = 23
        walls2ID[er][oc][botLftCor] = 23
        walls2ID[or][oc][botLftCor] = 23
        walls2ID[or][ec][botLftCor] = 23

        // Hallways
        walls2ID[er][ec][vHallway] = 24
        walls2ID[er][oc][vHallway] = 24
        walls2ID[or][oc][vHallway] = 24
        walls2ID[or][ec][vHallway] = 24

        walls2ID[er][ec][hHallway] = 25
        walls2ID[er][oc][hHallway] = 25
        walls2ID[or][oc][hHallway] = 25
        walls2ID[or][ec][hHallway] = 25

        // Initialize the id2Links lookup table
        id2Links[0] = intArrayOf(1, 0, 0, 0)
        id2Links[1] = intArrayOf(0, 0, 0, 1)
        id2Links[2] = intArrayOf(0, 0, 1, 0)
        id2Links[3] = intArrayOf(0, 1, 0, 0)
        id2Links[4] = intArrayOf(0, 0, 0, 1)
        id2Links[5] = intArrayOf(0, 0, 1, 0)
        id2Links[6] = intArrayOf(0, 1, 0, 0)
        id2Links[7] = intArrayOf(1, 0, 0, 0)
        id2Links[8] = intArrayOf(0, 1, 0, 0)
        id2Links[9] = intArrayOf(1, 0, 0, 0)
        id2Links[10] = intArrayOf(0, 0, 1, 0)
        id2Links[11] = intArrayOf(0, 0, 0, 1)
        id2Links[12] = intArrayOf(0, 0, 1, 0)
        id2Links[13] = intArrayOf(0, 0, 0, 1)
        id2Links[14] = intArrayOf(1, 0, 0, 0)
        id2Links[15] = intArrayOf(0, 1, 0, 0)
        id2Links[16] = intArrayOf(1, 0, 0, 1)
        id2Links[17] = intArrayOf(1, 0, 1, 0)
        id2Links[18] = intArrayOf(0, 1, 0, 1)
        id2Links[19] = intArrayOf(0, 1, 1, 0)
        id2Links[20] = intArrayOf(1, 0, 1, 0)
        id2Links[21] = intArrayOf(1, 0, 0, 1)
        id2Links[22] = intArrayOf(0, 1, 1, 0)
        id2Links[23] = intArrayOf(0, 1, 0, 1)
        id2Links[24] = intArrayOf(1, 1, 1, 1)
        id2Links[25] = intArrayOf(1, 1, 1, 1)
        id2Links[26] = intArrayOf(1, 1, 1, 1)
    }

    override fun generatePuzzle(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
        seed: Long?,
        isFlipOU: Boolean
    ): Puzzle {

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random.Default

        // Create a wall matrix to represent walls for each cell
        val wallMatrix = Array(rows) { row ->
            IntArray(cols) { col ->
                var walls = noWalls

                // Add outer boundary walls
                if (row == 0) walls += topWall
                if (col == 0) walls += lftWall
                if (row == rows - 1) walls += botWall
                if (col == cols - 1) walls += rhtWall

                walls
            }
        }

        // Place interior walls according to the Celtic knot strategy
        // Place walls strategically to ensure a proper puzzle
        placeInteriorWalls(wallMatrix, random)

        // Create the puzzle matrix based on the boundary matrix
        val puzzleMatrix = Array(rows) { row ->
            IntArray(cols) { col ->
                // Determine picture unit based on boundary walls and row/col parity
                determinePicUnit(wallMatrix, row, col, isFlipOU)
            }
        }

        return Puzzle(
            picUnitMatrix = puzzleMatrix,
            tileRows = tileRows,
            tileCols = tileCols,
            seed = seed
        )
    }

    override fun scramblePuzzle(
        puzzle: Puzzle,
        doRotations: Boolean,
    ): Puzzle {
        val picUnitMatrix = puzzle.picUnitMatrix

        // No need to scramble empty puzzles
        if (picUnitMatrix.isEmpty()) return puzzle

        // Calculate the picture unit matrix dimensions
        val picUnitRows = puzzle.rows
        val picUnitCols = puzzle.cols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols

        // Can't scramble if the picture unit matrix dimensions
        // are not divisible by the tile dimensions
        if (picUnitRows % tileRows != 0 || picUnitCols % tileCols != 0) {
            return puzzle
        }

        // Create a random number generator, optionally with a seed
        val random = if (puzzle.seed != null) Random(puzzle.seed) else Random.Default

        // Create a list of all tile positions and...
        val shuffledPositions = mutableListOf<Pair<Int, Int>>()
        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                shuffledPositions.add(Pair(boardRow, boardCol))
            }
        }
        // ...shuffle them
        shuffledPositions.shuffle(random)

        val scrambledMatrix = Array(picUnitRows) { IntArray(picUnitCols) }

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                val newGamePos = shuffledPositions[(boardRow * boardCols) + boardCol]
                // Calculate the rotation (0, 90, 180, or 270 degrees)
                // Only rotate if tiles are square
                val quarterTurns = if (doRotations && tileRows == tileCols) {
                    random.nextInt(4)
                } else {
                    0
                }

                val squareSize = tileRows  // Square size is the same as tile rows or columns

                // Copy and rotate the tile
                for (tileRow in 0 until squareSize) {
                    for (tileCol in 0 until squareSize) {
                        val srcRow = boardRow * squareSize + tileRow
                        val srcCol = boardCol * squareSize + tileCol

                        // Calculate the rotated position of the picture unit
                        val (rotatedTileRow, rotatedTileCol) = when (quarterTurns) {
                            0 -> Pair(tileRow, tileCol)
                            1 -> Pair(tileCol, squareSize - 1 - tileRow)
                            2 -> Pair(squareSize - 1 - tileRow, squareSize - 1 - tileCol)
                            3 -> Pair(squareSize - 1 - tileCol, tileRow)
                            else -> Pair(tileRow, tileCol) // Default to no rotation
                        }

                        // Calculate the destination position in the scrambled matrix
                        val destRow = newGamePos.first * tileRows + rotatedTileRow
                        val destCol = newGamePos.second * tileCols + rotatedTileCol

                        // Rotate the picture unit and copy it to the destination position
                        scrambledMatrix[destRow][destCol] = rotatePicUnit(
                            picUnit = picUnitMatrix[srcRow][srcCol],
                            quarterTurns = quarterTurns
                        )
                    }
                }
            }
        }

        return Puzzle(
            picUnitMatrix = scrambledMatrix,
            tileRows = tileRows,
            tileCols = tileCols,
            seed = puzzle.seed
        )
    }

    override fun swapTiles(
        puzzle: Puzzle,
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        val picUnitMatrix = puzzle.picUnitMatrix
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val startRow1 = boardRow1 * tileRows
        val startCol1 = boardCol1 * tileCols
        val startRow2 = boardRow2 * tileRows
        val startCol2 = boardCol2 * tileCols

        // Swap the picture units
        for (row in 0 until tileRows) {
            for (col in 0 until tileCols) {
                val row1 = startRow1 + row
                val col1 = startCol1 + col
                val row2 = startRow2 + row
                val col2 = startCol2 + col
                val temp = picUnitMatrix[row1][col1]
                picUnitMatrix[row1][col1] = picUnitMatrix[row2][col2]
                picUnitMatrix[row2][col2] = temp
            }
        }
    }

    override fun rotateTile(
        puzzle: Puzzle,
        boardRow: Int,
        boardCol: Int,
    ) {
        val picUnitMatrix = puzzle.picUnitMatrix
        val squareTileSize = puzzle.tileRows // Square size is the same as tile rows or columns

        val startRow = boardRow * squareTileSize
        val startCol = boardCol * squareTileSize

        // Determine the rotated tile
        val rotatedTile = Array(squareTileSize) { IntArray(squareTileSize) }
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                rotatedTile[col][squareTileSize - 1 - row] =
                    rotatePicUnit(picUnitMatrix[startRow + row][startCol + col], 1)
            }
        }

        // Replace the original tile with the rotated tile
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                picUnitMatrix[startRow + row][startCol + col] = rotatedTile[row][col]
            }
        }
    }

    private fun placeInteriorWalls(
        wallMatrix: Array<IntArray>,
        random: Random
    ) {
        val rows = wallMatrix.size
        val cols = wallMatrix[0].size

        // Create a list of all possible placement positions
        var maxPlacements = 0
        val placementPositions = mutableListOf<Pair<Int, Int>>()
        for (row in 0 until rows - 1) {
            for (col in 0 until cols - 1) {
                // Only consider positions where (row + col) % 2 == 1
                if ((row + col) % 2 == 1) {
                    placementPositions.add(Pair(row, col))
                    ++maxPlacements
                }
            }
        }

        // Randomly choose the number of wall placements to make (experience shows
        // between 1/3 of max and max is good)
        val numPlacements = random.nextInt(maxPlacements / 3, maxPlacements + 1)

        // Make random placements
        placementPositions.shuffle(random)
        for (i in 0 until numPlacements) {
            val (row, col) = placementPositions[i]

            // Randomly decide whether to place vertical or horizontal walls
            val isVertical = random.nextBoolean()

            if (isVertical) {
                // Place vertical walls
                wallMatrix[row][col] += rhtWall
                wallMatrix[row][col + 1] += lftWall
                wallMatrix[row + 1][col] += rhtWall
                wallMatrix[row + 1][col + 1] += lftWall
            } else {
                // Place horizontal walls
                wallMatrix[row][col] += botWall
                wallMatrix[row][col + 1] += botWall
                wallMatrix[row + 1][col] += topWall
                wallMatrix[row + 1][col + 1] += topWall
            }
        }
    }

    private fun determinePicUnit(
        wallMatrix: Array<IntArray>,
        row: Int,
        col: Int,
        isFlipOU: Boolean
    ): Int {
        // Get the walls for this cell
        val walls = wallMatrix[row][col]

        // Determine the row and column parity (even/odd)
        // If isFlipOU is true, flip the parity by adding 1 before taking modulo
        val rowParity = if (isFlipOU) (row + 1) % 2 else row % 2
        val colParity = if (isFlipOU) (col + 1) % 2 else col % 2

        // Look up the picture Unit ID in our table
        return walls2ID[rowParity][colParity][walls]
    }
}