package com.challanty.android.kp3.di

import com.challanty.android.kp3.puzzle.PuzzleService
import com.challanty.android.kp3.puzzle.PuzzleServiceFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import javax.inject.Singleton

@Module
@InstallIn(ViewModelComponent::class) // Or SingletonComponent::class if needed app-wide
object PuzzleServiceModule {

    @Provides
    @Singleton
    @OriginalPuzzle
    fun provideOriginalPuzzleService(): PuzzleService {
        return PuzzleServiceFactory.createService(PuzzleServiceFactory.ServiceType.ORIGINAL)
    }

// You can add more for other types, e.g., @ExpertPuzzle, @DailyChallengePuzzle, etc.

}