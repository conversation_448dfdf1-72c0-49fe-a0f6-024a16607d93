package com.challanty.android.kp3.viewModel

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.unit.IntSize
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.pictureUnits.original.CelticPictureUnitFactory
import com.challanty.android.kp3.pictureUnits.original.CelticPicUnitStrategy
import com.challanty.android.kp3.puzzle.Puzzle
import com.challanty.android.kp3.puzzle.PuzzleService
import com.challanty.android.kp3.puzzle.PuzzleServiceFactory
import com.challanty.android.kp3.puzzle.original.OriginalPuzzleService
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import com.challanty.android.kp3.util.toneOnToneColor
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.random.Random

/**
 * ViewModel for managing the background puzzle.
 * Handles generation and state management of the Celtic knot background pattern.
 */
@HiltViewModel
class BackgroundViewModel @Inject constructor(
    private val processingStateManager: ProcessingStateManager
) : ViewModel() {
    // TODO: Make PuzzleService an injected dependency
    val puzzleService =
        PuzzleServiceFactory.createService(PuzzleServiceFactory.ServiceType.ORIGINAL)

    // The background color for the puzzle
    // This is set from AppRoot with MaterialTheme.colorScheme.background
    // Note: We don't make backgroundColor a parameter because it needs to be set to
    // a value only available in a Composable context which may not always be
    // available when the ViewModel is created.
    private var backgroundColor: Color? = null

    // A size in pixels for small versions of the 26 Celtic knot picture units
    // where the Celtic knots can still be discerned.
    private val pictureUnitSize = 40f

    // StateFlow for keeping the background up-to-date
    private val _backgroundBitmap = MutableStateFlow<ImageBitmap?>(null)
    val backgroundBitmap: StateFlow<ImageBitmap?> = _backgroundBitmap

    private var curPuzDisplaySize = IntSize.Zero

    /**
     * Updates the background color and regenerates the background if needed.
     * This method should be called from a composable context where MaterialTheme is available.
     *
     * @param color The new background color
     */
    fun updateBackgroundColor(color: Color) {
        if (backgroundColor != color) {
            backgroundColor = color

            // Generate the background if we have a valid size
            if (curPuzDisplaySize != IntSize.Zero) {
                generateBackground()
            }
        }
    }

    fun onBGDisplaySizeChanged(size: IntSize) {
        // Ignore startup and redundant size changes
        if (size == IntSize.Zero || size == curPuzDisplaySize) return

        curPuzDisplaySize = size

        generateBackground()
    }

    fun generateBackground(

    ) {

        // Calculate how many cells we need to cover the screen
        var cols = (curPuzDisplaySize.width / pictureUnitSize).toInt() + 2
        var rows = (curPuzDisplaySize.height / pictureUnitSize).toInt() + 2

        // Rows and columns must be even numbers for generator to work
        if (cols % 2 != 0) cols++
        if (rows % 2 != 0) rows++

        viewModelScope.launch {

            processingStateManager.startProcessing(ProcessingType.BACKGROUND)

            // TODO do we really need withContext(Dispatchers.Default) here?
            val puzzle = withContext(Dispatchers.Default) {
                generatePuzzle(
                    rows = rows,
                    cols = cols,
                    seed = Random.nextLong()
                )
            }

            generateBitmap(puzzle, curPuzDisplaySize, backgroundColor ?: Color.Gray)

            processingStateManager.endProcessing(ProcessingType.BACKGROUND)
        }
    }

    private fun generatePuzzle(
        rows: Int,
        cols: Int,
        seed: Long = Random.nextLong()
    ): Puzzle {
        return puzzleService.generatePuzzle(
            rows = rows,
            cols = cols,
            tileRows = 1,
            tileCols = 1,
            seed = seed
        )
    }

    private fun generateBitmap(
        puzzle: Puzzle,
        size: IntSize,
        backgroundColor: Color
    ) {
        // NOTE: We should already be on a background thread here
        // and BG_STARTUP processing should be active.
        // We are guaranteed to have a valid puzzle at this point

        val bitmap = ImageBitmap(size.width, size.height)
        val canvas = Canvas(bitmap)

        // Fill with background color first
        canvas.drawRect(
            0f, 0f, size.width.toFloat(), size.height.toFloat(),
            Paint().apply {
                this.color = backgroundColor
            }
        )

        drawBackgroundPuzzle(
            canvas = canvas,
            size = size,
            backgroundColor = backgroundColor,
            puzzle = puzzle
        )

        _backgroundBitmap.value = bitmap
    }

    private fun drawBackgroundPuzzle(
        canvas: Canvas,
        size: IntSize,
        backgroundColor: Color,
        puzzle: Puzzle,
    ) {
        val picUnitMatrix = puzzle.picUnitMatrix
        if (picUnitMatrix.isEmpty()) return

        val rows = puzzle.rows
        val cols = puzzle.cols

        // Calculate the offset to center the patterns
        val totalWidth = cols * pictureUnitSize
        val totalHeight = rows * pictureUnitSize
        val offsetX = (size.width - totalWidth) / 2
        val offsetY = (size.height - totalHeight) / 2

        // Get the pattern color
        val colorPair = toneOnToneColor(backgroundColor)

        // Create reusable paint objects to avoid creating new ones for each pattern
        val fillPaint = Paint().apply {
            this.color = colorPair.first
            this.style = PaintingStyle.Fill
        }

        val strokePaint = Paint().apply {
            this.color = colorPair.second
            this.style = PaintingStyle.Stroke
            this.strokeWidth = 2f
        }

        // Draw all picture units
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                val x = offsetX + col * pictureUnitSize
                val y = offsetY + row * pictureUnitSize

                val pictureUnitID = picUnitMatrix[row][col]
                val pictureUnitStrategy = CelticPictureUnitFactory.createPictureUnit(pictureUnitID)

                // Draw the pattern on the canvas
                drawCelticPattern(
                    canvas = canvas,
                    pictureUnitStrategy = pictureUnitStrategy,
                    x = x,
                    y = y,
                    cellSize = pictureUnitSize,
                    fillPaint = fillPaint,
                    strokePaint = strokePaint
                )
            }
        }
    }

    /**
     * Draws a Celtic pattern on a Canvas.
     *
     * @param canvas The canvas to draw on
     * @param pictureUnitStrategy The strategy for creating the picture unit paths
     * @param x The x-coordinate of the pattern
     * @param y The y-coordinate of the pattern
     * @param cellSize The size of the cell
     * @param fillPaint The paint for filling the pattern
     * @param strokePaint The paint for stroking the pattern
     */
    private fun drawCelticPattern(
        canvas: Canvas,
        pictureUnitStrategy: CelticPicUnitStrategy,
        x: Float,
        y: Float,
        cellSize: Float,
        fillPaint: Paint,
        strokePaint: Paint
    ) {
        // Save the canvas state
        canvas.save()

        // Translate to the pattern position
        canvas.translate(x, y)

        // Calculate the center point in local coordinates
        val localCenterX = cellSize / 2
        val localCenterY = cellSize / 2
        val radius = cellSize / 2

        // Create the paths for this pattern
        val paths = pictureUnitStrategy.createPaths(Offset(localCenterX, localCenterY), radius)

        // Draw the ribbon path
        paths.getRibbonPath().let { path ->
            // Draw the path with fill style
            canvas.drawPath(path, fillPaint)
        }

        // Draw the outline path
        paths.getOutlinePath().let { path ->
            // Draw the path with stroke style
            canvas.drawPath(path, strokePaint)
        }

        // Restore the canvas state
        canvas.restore()
    }
}
