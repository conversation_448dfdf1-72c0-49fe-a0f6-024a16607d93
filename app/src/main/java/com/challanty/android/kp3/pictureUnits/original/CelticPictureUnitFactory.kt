package com.challanty.android.kp3.pictureUnits.original

/**
 * Factory class for creating Celtic picture unit strategies.
 */
object CelticPictureUnitFactory {
    /**
     * Creates a Celtic picture unit strategy based on the picture unit type.
     * @param pictureUnitType The type of picture unit to create (0-25, corresponding to A-Z)
     * @return The created Celtic picture unit strategy
     */
    fun createPictureUnit(pictureUnitType: Int): CelticPicUnitStrategy {
        // TODO this produces an optical illusion!!
//        return CelticPicUnitB()
        return when (pictureUnitType % 26) {
            0 -> CelticPicUnitA()
            1 -> CelticPicUnitB()
            2 -> CelticPicUnitC()
            3 -> CelticPicUnitD()
            4 -> CelticPicUnitE()
            5 -> CelticPicUnitF()
            6 -> CelticPicUnitG()
            7 -> CelticPicUnitH()
            8 -> CelticPicUnitI()
            9 -> CelticPicUnitJ()
            10 -> CelticPicUnitK()
            11 -> CelticPicUnitL()
            12 -> CelticPicUnitM()
            13 -> CelticPicUnitN()
            14 -> CelticPicUnitO()
            15 -> CelticPicUnitP()
            16 -> CelticPicUnitQ()
            17 -> CelticPicUnitR()
            18 -> CelticPicUnitS()
            19 -> CelticPicUnitT()
            20 -> CelticPicUnitU()
            21 -> CelticPicUnitV()
            22 -> CelticPicUnitW()
            23 -> CelticPicUnitX()
            24 -> CelticPicUnitY()
            25 -> CelticPicUnitZ()
            else -> CelticPicUnitA() // Default fallback
        }
    }
}