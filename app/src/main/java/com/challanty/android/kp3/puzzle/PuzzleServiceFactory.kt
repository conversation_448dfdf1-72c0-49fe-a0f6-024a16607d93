package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.puzzle.original.OriginalPuzzleService

/**
 * Factory for creating puzzle generators.
 * Follows the factory pattern to create different types of puzzle generators.
 */
object PuzzleServiceFactory {
    /**
     * Type of puzzle service.
     */
    enum class ServiceType {
        ORIGINAL
        // More generator types can be added in the future
    }

    /**
     * Creates a puzzle service of the specified type.
     *
     * @param type The type of puzzle service to create
     * @return A puzzle service instance
     */
    fun createService(type: ServiceType): PuzzleService {
        return when (type) {
            ServiceType.ORIGINAL -> OriginalPuzzleService()
        }
    }
}
