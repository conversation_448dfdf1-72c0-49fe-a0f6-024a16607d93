package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.viewModel.TileModel

/**
 * Interface for puzzle generators.
 * Defines methods to generate and manipulate puzzles for the Celtic knot game.
 */
interface PuzzleService {

    fun generatePuzzle(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
        seed: Long? = null,
        isFlipOU: Boolean = false
    ): Puzzle

    fun scramblePuzzle(
        puzzle: Puzzle,
        doRotations: <PERSON><PERSON><PERSON>,
    ): Puzzle

    fun swapTiles(
        puzzle: Puzzle,
        tile1: TileModel,
        tile2: TileModel,
    )

    fun rotateTile(
        puzzle: Puzzle,
        tile: TileModel,
    )

    fun isPuzzleSolved(
        puzzle: Puzzle,
    ): <PERSON><PERSON><PERSON>
}
