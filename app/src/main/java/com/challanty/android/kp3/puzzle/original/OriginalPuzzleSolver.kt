package com.challanty.android.kp3.puzzle.original

import com.challanty.android.kp3.puzzle.Puzzle
import com.challanty.android.kp3.puzzle.original.PicUnitLinks.Companion.oppSide

// links = The type of link for the top, right, bottom, and left edges
// of a picture unit. The links of the sides of proper neighboring pic units always
// add to zero (the sides have no links) or 6 (the sides link properly).
// 0 = no link
// 1 = left corner link going over
// 2 = left corner link going under
// 3 = middle link
// 4 = right corner link going over
// 5 = right corner link going under
//
//      L  M  R   L/M/R = left/middle/right
//      -------
//    R |     | L
//    M |     | M
//    L |     | R
//      -------
//      R  M  L

const val NO_LINK = 0
const val LFT_OVER_LINK = 1
const val LFT_UNDER_LINK = 2
const val MID_LINK = 3
const val RHT_OVER_LINK = 4
const val RHT_UNDER_LINK = 5

const val TOP_SIDE = 0
const val RHT_SIDE = 1
const val BOT_SIDE = 2
const val LFT_SIDE = 3

// The type of link at each side of a picture unit
class PicUnitLinks(
    private val top: Int,
    private val rht: Int,
    private val bot: Int,
    private val lft: Int
) {

    companion object {
        fun oppSide(side: Int): Int {
            return (side + 2) % 4
        }
    }

    private val links = intArrayOf(top, rht, bot, lft)

    fun quarterRotated(rotations: Int): PicUnitLinks {
        return when (rotations % 4) {
            0 -> PicUnitLinks(top, rht, bot, lft)
            1 -> PicUnitLinks(lft, top, rht, bot)
            2 -> PicUnitLinks(bot, lft, top, rht)
            else -> PicUnitLinks(rht, bot, lft, top)
        }
    }

    fun getLink(side: Int): Int {
        return links[side]
    }
}

class OriginalPuzzleSolver {

    private val borderPicUnitLinks = PicUnitLinks(NO_LINK, NO_LINK, NO_LINK, NO_LINK)
    private val picUnitALinks = PicUnitLinks(LFT_OVER_LINK, NO_LINK, MID_LINK, RHT_OVER_LINK)
    private val picUnitELinks = PicUnitLinks(LFT_UNDER_LINK, NO_LINK, MID_LINK, RHT_UNDER_LINK)
    private val picUnitILinks = PicUnitLinks(RHT_OVER_LINK, LFT_OVER_LINK, MID_LINK, NO_LINK)
    private val picUnitMLinks = PicUnitLinks(RHT_UNDER_LINK, LFT_UNDER_LINK, MID_LINK, NO_LINK)
    private val picUnitQLinks = PicUnitLinks(LFT_UNDER_LINK, RHT_OVER_LINK, LFT_OVER_LINK, RHT_UNDER_LINK)
    private val picUnitULinks = PicUnitLinks(NO_LINK, MID_LINK, MID_LINK, NO_LINK)
    private val picUnitYLinks = PicUnitLinks(MID_LINK, NO_LINK, MID_LINK, NO_LINK)

    private val id2Links = Array(26) { borderPicUnitLinks }

    init {
        id2Links[0] = picUnitALinks
        id2Links[1] = picUnitALinks.quarterRotated(1)
        id2Links[2] = picUnitALinks.quarterRotated(2)
        id2Links[3] = picUnitALinks.quarterRotated(3)
        id2Links[4] = picUnitELinks
        id2Links[5] = picUnitELinks.quarterRotated(1)
        id2Links[6] = picUnitELinks.quarterRotated(2)
        id2Links[7] = picUnitELinks.quarterRotated(3)
        id2Links[8] = picUnitILinks
        id2Links[9] = picUnitILinks.quarterRotated(1)
        id2Links[10] = picUnitILinks.quarterRotated(2)
        id2Links[11] = picUnitILinks.quarterRotated(3)
        id2Links[12] = picUnitMLinks
        id2Links[13] = picUnitMLinks.quarterRotated(1)
        id2Links[14] = picUnitMLinks.quarterRotated(2)
        id2Links[15] = picUnitMLinks.quarterRotated(3)
        id2Links[16] = picUnitQLinks
        id2Links[17] = picUnitQLinks.quarterRotated(1)
        id2Links[18] = picUnitQLinks.quarterRotated(2)
        id2Links[19] = picUnitQLinks.quarterRotated(3)
        id2Links[20] = picUnitULinks
        id2Links[21] = picUnitULinks.quarterRotated(1)
        id2Links[22] = picUnitULinks.quarterRotated(2)
        id2Links[23] = picUnitULinks.quarterRotated(3)
        id2Links[24] = picUnitYLinks
        id2Links[25] = picUnitYLinks.quarterRotated(1)
        id2Links[26] = borderPicUnitLinks
    }

    fun isPuzzleSolved(puzzle: Puzzle): Boolean {
        val picUnitMatrix = puzzle.picUnitMatrix
        val picUnitRows = puzzle.rows
        val picUnitCols = puzzle.cols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols

        // Check all interior left walls of tiles
        for (boardRow in 0 until boardRows) {
            for (boardCol in 1 until boardCols) {
                for (tileRow in 0 until tileRows) {
                    val startRow = boardRow * tileRows
                    val startCol = boardCol * tileCols
                    val picUnit1 = picUnitMatrix[startRow + tileRow][startCol]
                    val picUnit2 = picUnitMatrix[startRow + tileRow][startCol - 1]

                    if (!areProperNeighbors(picUnit1, picUnit2, LFT_SIDE)) {
                        println("Left wall failure at $startRow/$startCol for tile at $boardRow/$boardCol")
                        return false
                    }
                }
            }
        }

        // Check all interior bottom walls of tiles
        for (boardRow in 0 until boardRows - 1) {
            for (boardCol in 0 until boardCols) {
                for (tileCol in 0 until tileCols) {
                    val startRow = (boardRow * tileRows) + tileRows - 1
                    val startCol = boardCol * tileCols
                    val picUnit1 = picUnitMatrix[startRow][startCol + tileCol]
                    val picUnit2 = picUnitMatrix[startRow + 1][startCol + tileCol]

                    if (!areProperNeighbors(picUnit1, picUnit2, BOT_SIDE)) {
                        println("Bottom wall failure at $startRow/$startCol for tile at $boardRow/$boardCol")
                        return false
                    }
                }
            }
        }

        // Check all exterior left and right walls
        val startCol = 0
        val endCol = picUnitCols - 1
        for (boardRow in 0 until boardRows) {
            for (tileRow in 0 until tileRows) {
                val startRow = boardRow * tileRows
                val picUnit1 = picUnitMatrix[startRow + tileRow][startCol]
                val picUnit2 = picUnitMatrix[startRow + tileRow][endCol]

                if (!areProperNeighbors(picUnit1, 26, LFT_SIDE)) {
                    println("Left wall failure at $startRow/$startCol")
                    return false
                }

                if (!areProperNeighbors(picUnit2, 26, RHT_SIDE)) {
                    println("Right wall failure at $startRow/$endCol")
                    return false
                }
            }
        }

        // Check all exterior top and bottom walls
        val startRow = 0
        val endRow = picUnitRows - 1
        for (boardCol in 0 until boardCols) {
            for (tileCol in 0 until tileCols) {
                val startCol = boardCol * tileCols + tileCol
                val picUnit1 = picUnitMatrix[startRow][startCol]
                val picUnit2 = picUnitMatrix[endRow][startCol]

                if (!areProperNeighbors(picUnit1, 26, TOP_SIDE)) {
                    println("Top wall failure at $startRow/$startCol")
                    return false
                }


                if (!areProperNeighbors(picUnit2, 26, BOT_SIDE)) {
                    println("Bottom wall failure at $endRow/$startCol")
                    return false
                }
            }
        }

        return true
    }

    // Is the specified side of picUnit1 a proper neighbor for the opposite side of picUnit2?
    private fun areProperNeighbors(
        picUnit1: Int,
        picUnit2: Int,
        side: Int,
    ): Boolean {
        val linkSum = id2Links[picUnit1].getLink(side) + id2Links[picUnit2].getLink(oppSide(side))
        return linkSum == 0 || linkSum == 6
    }
}
