package com.challanty.android.kp3.viewModel.helper

import android.R.attr.height
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import com.challanty.android.kp3.data.repository.GameRepository
import com.challanty.android.kp3.pictureUnits.original.CelticPaths
import com.challanty.android.kp3.pictureUnits.original.CelticPictureUnitFactory
import com.challanty.android.kp3.puzzle.Puzzle
import com.challanty.android.kp3.puzzle.PuzzleService
import com.challanty.android.kp3.puzzle.PuzzleServiceFactory
import com.challanty.android.kp3.puzzle.original.OriginalPuzzleService
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.util.byteString2TwoDIntArray
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.challanty.android.kp3.viewModel.TileModel
import com.google.protobuf.ByteString
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject
import kotlin.math.min
import kotlin.random.Random

/**
 * Helper class for implementing GameViewModel business logic.
 */
@ViewModelScoped
class GameModelHelper @Inject constructor(
    private val puzzleService: PuzzleService,
    private val gameRepository: GameRepository,
    coroutineScope: CoroutineScope
) {

    val picUnitBoxSize = 100

    val ribbonPaint = Paint().apply {
        color = Color.Cyan // TODO: Make this a theme color
        style = PaintingStyle.Fill
        isAntiAlias = true
    }

    // Outline paint is recreated with each change to picUnitScale
    var outlinePaint: Paint = Paint()  // placeholder value

    // TODO change all repository access to use the StateFlow values directly?
//    private val _boardRows: StateFlow<Int> = gameRepository.getBoardRows()
//        .stateIn(
//            scope = coroutineScope,
//            started = SharingStarted.Eagerly, // Always active
//            initialValue = 0
//        )
//    val boardRows = _boardRows

    val boardRows: StateFlow<Int> = gameRepository.boardRows

    private val _boardCols: StateFlow<Int> = gameRepository.getBoardCols()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = 0
        )
    val boardCols = _boardCols

    private val _tileRows: StateFlow<Int> = gameRepository.getTileRows()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = 0
        )
    val tileRows = _tileRows

    private val _tileCols: StateFlow<Int> = gameRepository.getTileCols()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = 0
        )
    val tileCols = _tileCols

    private val _tilesRotatable: StateFlow<Boolean> = gameRepository.getTilesRotatable()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = false
        )
    val tilesRotatable = _tilesRotatable

    private val _board: StateFlow<ByteString> = gameRepository.getBoard()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = ByteString.EMPTY
        )
    val boardBytes = _board

    private val _solution: StateFlow<ByteString> = gameRepository.getSolution()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = ByteString.EMPTY
        )
    val solutionBytes = _solution

    private val _animate: StateFlow<Boolean> = gameRepository.getAnimate()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = false
        )
    val animate = _animate

    private val _animateRotation: StateFlow<Boolean> = gameRepository.getAnimateRotation()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = false
        )
    val animateRotation = _animateRotation

    private val _animateSwap: StateFlow<Boolean> = gameRepository.getAnimateSwap()
        .stateIn(
            scope = coroutineScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = false
        )
    val animateSwap = _animateSwap

    private var picUnitSize: Float = 0f
    private var pictUnitScale: Float = 0f
    private var picUnitRows: Int = 0
    private var picUnitColumns: Int = 0

    private var puzzle = Puzzle()

    fun buildNewGame(): GameState {

        puzzle = puzzleService.generatePuzzle(
            rows = picUnitRows,
            cols = picUnitColumns,
            tileRows = tileRows.value,
            tileCols = tileCols.value,
            seed = Random.nextLong(),
            isFlipOU = Random.nextBoolean()
        )
        gameRepository.setSolution(twoDintArray2ByteString(puzzle.picUnitMatrix))

        puzzle = puzzleService.scramblePuzzle(
            puzzle = puzzle,
            doRotations = tilesRotatable.value,
        )
        gameRepository.setBoard(twoDintArray2ByteString(puzzle.picUnitMatrix))

        return createGameResources(puzzle)
    }

    fun buildExistingGame(gamingAreaSize: IntSize? = null): GameState {

        if (gamingAreaSize != null) calculateGameDimensions(gamingAreaSize)

        puzzle = Puzzle(
            picUnitMatrix = byteString2TwoDIntArray(
                byteString = boardBytes.value,
                rows = picUnitRows,
                cols = picUnitColumns
            ),
            tileRows = tileRows.value,
            tileCols = tileCols.value
        )

        return createGameResources(puzzle)
    }

    fun handlePuzzleTileSwap(
        tile1: TileModel,
        tile2: TileModel
    ) {
        puzzleService.swapTiles(
            puzzle = puzzle,
            tile1 = tile1,
            tile2 = tile2,
        )

        gameRepository.setBoard(twoDintArray2ByteString(puzzle.picUnitMatrix))
    }

    fun handlePuzzleTileRotation(tile: TileModel) {
        // Rotate the puzzle pic units and quarter turn for the tile and save to repository
        puzzleService.rotateTile(
            puzzle = puzzle,
            tile = tile
        )

        gameRepository.setBoard(twoDintArray2ByteString(puzzle.picUnitMatrix))
    }

    private fun createGameResources(puzzle: Puzzle): GameState {

        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val tilePxSize = IntSize(
            width = (picUnitSize * tileCols).toInt(),
            height = (picUnitSize * tileRows).toInt()
        )

        val boardPxSize = IntSize(
            width = (picUnitSize * picUnitColumns).toInt(),
            height = (picUnitSize * picUnitRows).toInt()
        )

        outlinePaint = Paint().apply {
            this.color = Color.Black
            this.style = PaintingStyle.Stroke
            this.strokeWidth = 0.1f * pictUnitScale * picUnitSize
            this.strokeCap = StrokeCap.Square
            this.isAntiAlias = true
        }

        val winBitmap = ImageBitmap(
            width = boardPxSize.width,
            height = boardPxSize.height
        )
        val winCanvas = Canvas(winBitmap)

        // AI says the conventional way to locate a graphic is by its center and radius.
        val drawingRadius = picUnitSize / 2
        val drawingCenter = Offset(drawingRadius, drawingRadius)

        var tileID = 0
        val tileModelList = mutableListOf<TileModel>()

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {

                val tileBitmap = ImageBitmap(
                    width = tilePxSize.width,
                    height = tilePxSize.height
                )
                val tileCanvas = Canvas(tileBitmap)

                for (tileRow in 0 until tileRows) {
                    val puzzleRow = boardRow * tileRows + tileRow

                    for (tileCol in 0 until tileCols) {
                        val puzzleCol = boardCol * tileCols + tileCol

                        val pictureUnit = puzzle.getPictureUnitAt(
                            row = puzzleRow,
                            col = puzzleCol
                        )

                        val pictureUnitStrategy =
                            CelticPictureUnitFactory.createPictureUnit(pictureUnit)
                        val paths = pictureUnitStrategy.createPaths(drawingCenter, drawingRadius)

                        drawPictureUnit(
                            canvas = tileCanvas,
                            paths = paths,
                            x = tileCol * picUnitSize,
                            y = tileRow * picUnitSize,
                            ribbonPaint = ribbonPaint,
                            outlinePaint = outlinePaint
                        )
                        drawPictureUnit(
                            canvas = winCanvas,
                            paths = paths,
                            x = puzzleCol * picUnitSize,
                            y = puzzleRow * picUnitSize,
                            ribbonPaint = ribbonPaint,
                            outlinePaint = outlinePaint
                        )
                    }
                }

                tileModelList.add(
                    TileModel(
                        id = tileID,
                        bitmap = tileBitmap,
                        boardPosition = IntOffset(boardRow, boardCol),
                        initIntOffset = IntOffset.Zero,
                        initIntOffsetDuration = 0,
                        initQuarterTurnCnt = 0,
                        initRotationDuration = 0,
                        initIsLocked = false,
                        initIsSelected = false,
                    )
                )
                tileID++
            }
        }

        val (color1, color2) = generateWinColors()

        return GameState(
            showProgress = false,
            showGameBoard = true,
            showSolvedBoard = false,
            boardPxSize = boardPxSize,
            tilePxSize = tilePxSize,
            tileModels = tileModelList.toList(),
            solvedImage = winBitmap,
            solvedTintTargetColor = color1,
            solvedBGTargetColor = color2
        )
    }

    private fun drawPictureUnit(
        canvas: Canvas,
        paths: CelticPaths,
        x: Float,
        y: Float,
        ribbonPaint: Paint,
        outlinePaint: Paint
    ) {
        with(canvas) {
            save()
            translate(x, y)

            drawPath(paths.getRibbonPath(), ribbonPaint)
            drawPath(paths.getOutlinePath(), outlinePaint)

            restore()
        }
    }

    private fun calculateGameDimensions(
        gamingAreaSize: IntSize
    ) {

        picUnitRows = boardRows.value * tileRows.value
        picUnitColumns = boardCols.value * tileCols.value

        // Max possible picture unit pixel size if it could be rectangular
        val maxPicUnitHeight = gamingAreaSize.height / picUnitRows
        val maxPicUnitWidth = gamingAreaSize.width / picUnitColumns

        // Use the smaller width/height size to make the largest possible
        // square picture unit that fits within the gaming area
        picUnitSize = min(maxPicUnitWidth, maxPicUnitHeight).toFloat()

        pictUnitScale = picUnitSize / picUnitBoxSize

        generateOutlinePaint()
    }

    private fun generateWinColors(): Pair<Color, Color> {
        val r = Random.nextInt(0, 256)
        val g = Random.nextInt(0, 256)
        val b = Random.nextInt(0, 256)

        val color1 = Color(red = 255 - r, green = 255 - g, blue = 255 - b)
        val color2 = Color(red = r, green = g, blue = b)

        return Pair(color1, color2)
    }

    private fun generateOutlinePaint(): Paint {
        return Paint().apply {
            color = Color.Black // TODO: Make this a theme color
            style = PaintingStyle.Stroke
            strokeWidth = 10f * pictUnitScale
            strokeCap = StrokeCap.Square
            isAntiAlias = true
        }
    }

    // TODO move this to a test utility
    private fun showPicUnitMatrix(matrix: Array<IntArray>): String {
        // Convert the 2D array to a string representation
        // Convert the IDs 0-25 to their corresponding picture unit names a-z
        // without modifying the original array
        return matrix.joinToString("\n") { row ->
            row.joinToString("") { value ->
                (value % 26 + 97).toChar().toString()
            }
        }
    }
}
